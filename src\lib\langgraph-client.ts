'use client';

import { api } from './api';

// 注意：这个客户端不再直接使用LangGraph库
// 所有LangGraph逻辑都在后端处理，前端只负责API调用

// 前端 LangGraph 客户端服务
export class LangGraphClient {
  private baseUrl: string;

  constructor(baseUrl: string = '/api/langgraph') {
    this.baseUrl = baseUrl;
  }

  // 发送聊天消息
  async sendMessage(message: string, threadId?: string): Promise<any> {
    const response = await api.post(`${this.baseUrl}/chat`, { message, threadId });
    return response.data;
  }

  // 分析股票
  async analyzeStock(ticker: string, config: any = {}, threadId?: string): Promise<any> {
    const response = await api.post(`${this.baseUrl}/analyze`, { ticker, config, threadId });
    return response.data;
  }

  // 流式分析
  async *streamAnalysis(ticker: string, config: any = {}, threadId?: string): AsyncGenerator<any> {
    try {
      // 使用 axios 进行流式请求，设置 responseType 为 'stream'
      const response = await api.post(
        `${this.baseUrl}/stream`,
        { ticker, config, threadId },
        {
          responseType: 'stream',
          headers: {
            Accept: 'text/event-stream',
          },
        }
      );

      // 获取响应流
      const stream = response.data;
      const decoder = new TextDecoder();
      let buffer = '';

      // 创建一个 ReadableStream 来处理数据
      const reader = stream.getReader ? stream.getReader() : stream;

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                if (data.error) {
                  throw new Error(data.error);
                }
                yield data;
              } catch (parseError) {
                console.error('解析流数据失败:', parseError);
              }
            }
          }
        }
      } finally {
        if (reader.releaseLock) {
          reader.releaseLock();
        }
      }
    } catch (error) {
      console.error('流式分析请求失败:', error);
      throw new Error(`流式分析请求失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 获取会话状态
  async getSessionState(threadId: string): Promise<any> {
    try {
      const response = await api.get(`${this.baseUrl}/state`, {
        params: { threadId },
      });
      return response.data;
    } catch (error: any) {
      // 检查是否是 404 错误（会话不存在）
      if (error.response?.status === 404) {
        return null; // 会话不存在
      }
      throw new Error(`获取状态失败: ${error.response?.statusText || error.message}`);
    }
  }

  // 清除会话
  async clearSession(threadId: string): Promise<void> {
    try {
      await api.post(`${this.baseUrl}/state`, { threadId });
    } catch (error: any) {
      throw new Error(`清除会话失败: ${error.response?.statusText || error.message}`);
    }
  }

  // 删除会话
  async deleteSession(threadId: string): Promise<void> {
    try {
      await api.delete(`${this.baseUrl}/state`, {
        params: { threadId },
      });
    } catch (error: any) {
      throw new Error(`删除会话失败: ${error.response?.statusText || error.message}`);
    }
  }

  // 创建 WebSocket 连接信息
  async getWebSocketInfo(threadId: string): Promise<any> {
    try {
      const response = await api.get(`${this.baseUrl}/websocket`, {
        params: { threadId },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(`获取 WebSocket 信息失败: ${error.response?.statusText || error.message}`);
    }
  }
}

// 创建全局客户端实例
export const langGraphClient = new LangGraphClient();

// 导出类型定义
export interface ChatMessage {
  id: string;
  type: 'human' | 'ai' | 'tool' | 'system';
  content: string;
  timestamp: Date;
  metadata?: any;
}

export interface SessionState {
  threadId: string;
  messages: any[];
  currentStep: string;
  isProcessing: boolean;
  analysisResults?: any;
  tradingDecision?: any;
  error?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface AnalysisConfig {
  analysisType?: 'basic' | 'comprehensive' | 'quick';
  includeRisk?: boolean;
  includeSentiment?: boolean;
  includeNews?: boolean;
  timeframe?: '1d' | '1w' | '1m' | '3m' | '1y';
  [key: string]: any;
}
