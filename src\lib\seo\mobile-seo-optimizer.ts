/**
 * 移动端 SEO 优化器
 * 专门处理移动端的 SEO 优化需求
 */

export interface MobileOptimizationConfig {
  enableResponsiveDesign: boolean;
  enableMobileFirstIndexing: boolean;
  enableAMPSupport: boolean;
  enableTouchOptimization: boolean;
  enableMobileViewport: boolean;
  enableMobileSpeedOptimization: boolean;
  enableMobileFriendlyTest: boolean;
}

export interface MobilePerformanceMetrics {
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  timeToInteractive: number;
  speedIndex: number;
}

export interface ViewportConfig {
  width: string;
  initialScale: number;
  maximumScale?: number;
  minimumScale?: number;
  userScalable?: boolean;
  viewportFit?: 'auto' | 'contain' | 'cover';
}

export class MobileSEOOptimizer {
  private config: MobileOptimizationConfig;

  constructor(config?: Partial<MobileOptimizationConfig>) {
    this.config = {
      enableResponsiveDesign: true,
      enableMobileFirstIndexing: true,
      enableAMPSupport: false,
      enableTouchOptimization: true,
      enableMobileViewport: true,
      enableMobileSpeedOptimization: true,
      enableMobileFriendlyTest: true,
      ...config,
    };
  }

  /**
   * 生成移动端视口配置
   */
  generateViewportConfig(): ViewportConfig {
    return {
      width: 'device-width',
      initialScale: 1.0,
      maximumScale: 5.0,
      minimumScale: 1.0,
      userScalable: true,
      viewportFit: 'cover',
    };
  }

  /**
   * 生成移动端元标签
   */
  generateMobileMetaTags(): Array<{ name: string; content: string }> {
    const metaTags = [];

    if (this.config.enableMobileViewport) {
      const viewport = this.generateViewportConfig();
      const viewportContent = [
        `width=${viewport.width}`,
        `initial-scale=${viewport.initialScale}`,
        viewport.maximumScale && `maximum-scale=${viewport.maximumScale}`,
        viewport.minimumScale && `minimum-scale=${viewport.minimumScale}`,
        viewport.userScalable !== undefined &&
          `user-scalable=${viewport.userScalable ? 'yes' : 'no'}`,
        viewport.viewportFit && `viewport-fit=${viewport.viewportFit}`,
      ]
        .filter(Boolean)
        .join(', ');

      metaTags.push({
        name: 'viewport',
        content: viewportContent,
      });
    }

    // 移动端浏览器配置
    metaTags.push(
      {
        name: 'mobile-web-app-capable',
        content: 'yes',
      },
      {
        name: 'apple-mobile-web-app-capable',
        content: 'yes',
      },
      {
        name: 'apple-mobile-web-app-status-bar-style',
        content: 'black-translucent',
      },
      {
        name: 'apple-mobile-web-app-title',
        content: 'TradingAgents',
      },
      {
        name: 'application-name',
        content: 'TradingAgents',
      },
      {
        name: 'msapplication-TileColor',
        content: '#000000',
      },
      {
        name: 'theme-color',
        content: '#000000',
      },
      {
        name: 'format-detection',
        content: 'telephone=no, email=no, address=no',
      }
    );

    return metaTags;
  }

  /**
   * 检测移动设备
   */
  detectMobileDevice(): {
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
    deviceType: 'mobile' | 'tablet' | 'desktop';
    userAgent: string;
  } {
    if (typeof window === 'undefined') {
      return {
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        deviceType: 'desktop',
        userAgent: '',
      };
    }

    const userAgent = navigator.userAgent.toLowerCase();
    const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
      userAgent
    );
    const isTablet = /ipad|android(?!.*mobile)|tablet/i.test(userAgent);
    const isDesktop = !isMobile && !isTablet;

    let deviceType: 'mobile' | 'tablet' | 'desktop' = 'desktop';
    if (isMobile && !isTablet) deviceType = 'mobile';
    else if (isTablet) deviceType = 'tablet';

    return {
      isMobile: isMobile && !isTablet,
      isTablet,
      isDesktop,
      deviceType,
      userAgent: navigator.userAgent,
    };
  }

  /**
   * 优化移动端触摸体验
   */
  optimizeTouchExperience(): void {
    if (typeof window === 'undefined' || !this.config.enableTouchOptimization) return;

    // 添加触摸优化样式
    const style = document.createElement('style');
    style.textContent = `
      /* 移动端触摸优化 */
      * {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      /* 允许文本选择的元素 */
      input, textarea, [contenteditable] {
        -webkit-user-select: text;
        -khtml-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
      }

      /* 触摸目标最小尺寸 */
      button, a, input[type="button"], input[type="submit"] {
        min-height: 44px;
        min-width: 44px;
        touch-action: manipulation;
      }

      /* 快速点击优化 */
      a, button, input, select, textarea {
        touch-action: manipulation;
      }

      /* 滚动优化 */
      .scroll-container {
        -webkit-overflow-scrolling: touch;
        overflow-scrolling: touch;
      }

      /* 防止缩放 */
      input[type="text"], input[type="email"], input[type="password"], 
      input[type="number"], input[type="tel"], input[type="url"], 
      input[type="search"], textarea, select {
        font-size: 16px; /* 防止 iOS Safari 缩放 */
      }
    `;

    document.head.appendChild(style);

    // 添加触摸事件优化
    this.addTouchEventOptimization();
  }

  /**
   * 添加触摸事件优化
   */
  private addTouchEventOptimization(): void {
    if (typeof document === 'undefined') return;
    // 防止双击缩放
    let lastTouchEnd = 0;
    document.addEventListener(
      'touchend',
      (event) => {
        const now = Date.now();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      },
      { passive: false }
    );

    // 优化滚动性能
    document.addEventListener(
      'touchstart',
      () => {
        // 触摸开始时的优化
      },
      { passive: true }
    );

    document.addEventListener(
      'touchmove',
      (event) => {
        // 防止过度滚动
        const target = event.target as Element;
        if (!target.closest('.scroll-container')) {
          // 如果不在可滚动容器内，可能需要阻止默认行为
        }
      },
      { passive: true }
    );
  }

  /**
   * 优化移动端性能
   */
  optimizeMobilePerformance(): void {
    if (typeof window === 'undefined' || !this.config.enableMobileSpeedOptimization) return;

    // 延迟加载非关键资源
    this.deferNonCriticalResources();

    // 优化图片加载
    this.optimizeMobileImages();

    // 减少重绘和回流
    this.reduceReflowAndRepaint();

    // 优化动画性能
    this.optimizeAnimations();
  }

  /**
   * 延迟加载非关键资源
   */
  private deferNonCriticalResources(): void {
    if (typeof document === 'undefined') return;
    // 延迟加载非关键 JavaScript
    const scripts = document.querySelectorAll('script[data-mobile-defer="true"]');
    scripts.forEach((script) => {
      const newScript = document.createElement('script');
      newScript.src = script.getAttribute('src') || '';
      newScript.defer = true;

      // 在页面加载完成后加载
      if (document.readyState === 'complete') {
        document.head.appendChild(newScript);
      } else {
        window.addEventListener('load', () => {
          document.head.appendChild(newScript);
        });
      }
    });

    // 延迟加载非关键 CSS
    const stylesheets = document.querySelectorAll(
      'link[rel="stylesheet"][data-mobile-defer="true"]'
    );
    stylesheets.forEach((link) => {
      link.setAttribute('media', 'print');
      link.addEventListener('load', () => {
        link.setAttribute('media', 'all');
      });
    });
  }

  /**
   * 优化移动端图片
   */
  private optimizeMobileImages(): void {
    if (typeof document === 'undefined') return;
    const images = document.querySelectorAll('img');
    images.forEach((img) => {
      // 添加懒加载
      if (!img.hasAttribute('loading')) {
        img.setAttribute('loading', 'lazy');
      }

      // 添加解码优化
      if (!img.hasAttribute('decoding')) {
        img.setAttribute('decoding', 'async');
      }

      // 移动端图片尺寸优化
      if (window.innerWidth <= 768) {
        const src = img.getAttribute('src');
        if (src && !src.includes('w_auto') && !src.includes('mobile')) {
          // 这里可以添加图片 CDN 的移动端优化参数
          // 例如：img.src = src.replace(/(\.[^.]+)$/, '_mobile$1');
        }
      }
    });
  }

  /**
   * 减少重绘和回流
   */
  private reduceReflowAndRepaint(): void {
    if (typeof document === 'undefined') return;
    // 批量 DOM 操作
    const style = document.createElement('style');
    style.textContent = `
      /* 减少重绘和回流 */
      * {
        box-sizing: border-box;
      }

      /* 使用 transform 代替改变位置 */
      .animate-position {
        transform: translateZ(0);
        will-change: transform;
      }

      /* 避免复杂的 CSS 选择器 */
      .mobile-optimized {
        contain: layout style paint;
      }

      /* 使用 GPU 加速 */
      .gpu-accelerated {
        transform: translateZ(0);
        backface-visibility: hidden;
        perspective: 1000px;
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * 优化动画性能
   */
  private optimizeAnimations(): void {
    if (typeof document === 'undefined') return;
    // 检测设备性能
    const isLowEndDevice = this.detectLowEndDevice();

    if (isLowEndDevice) {
      // 在低端设备上禁用或简化动画
      const style = document.createElement('style');
      style.textContent = `
        /* 低端设备动画优化 */
        *, *::before, *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      `;
      document.head.appendChild(style);
    } else {
      // 优化动画性能
      const style = document.createElement('style');
      style.textContent = `
        /* 动画性能优化 */
        .animate {
          will-change: transform, opacity;
          transform: translateZ(0);
        }

        .animate:not(.animating) {
          will-change: auto;
        }
      `;
      document.head.appendChild(style);
    }
  }

  /**
   * 检测低端设备
   */
  private detectLowEndDevice(): boolean {
    if (typeof navigator === 'undefined') return false;

    // 检查硬件并发数
    const hardwareConcurrency = (navigator as any).hardwareConcurrency || 1;
    if (hardwareConcurrency <= 2) return true;

    // 检查内存
    const deviceMemory = (navigator as any).deviceMemory;
    if (deviceMemory && deviceMemory <= 2) return true;

    // 检查连接速度
    const connection = (navigator as any).connection;
    if (connection) {
      const effectiveType = connection.effectiveType;
      if (effectiveType === 'slow-2g' || effectiveType === '2g') return true;
    }

    return false;
  }

  /**
   * 生成 AMP 页面配置
   */
  generateAMPConfig(): {
    enabled: boolean;
    ampUrl?: string;
    canonicalUrl?: string;
  } {
    if (!this.config.enableAMPSupport) {
      return { enabled: false };
    }

    const currentUrl = typeof window !== 'undefined' ? window.location.href : '';
    const ampUrl = currentUrl.replace(/\/$/, '') + '/amp';

    return {
      enabled: true,
      ampUrl,
      canonicalUrl: currentUrl,
    };
  }

  /**
   * 移动端友好性测试
   */
  async runMobileFriendlyTest(): Promise<{
    passed: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    if (!this.config.enableMobileFriendlyTest) {
      return {
        passed: true,
        issues: [],
        recommendations: [],
      };
    }

    const issues: string[] = [];
    const recommendations: string[] = [];

    // 检查视口配置
    const viewportMeta = document.querySelector('meta[name="viewport"]');
    if (!viewportMeta) {
      issues.push('缺少视口元标签');
      recommendations.push(
        '添加 <meta name="viewport" content="width=device-width, initial-scale=1">'
      );
    }

    // 检查触摸目标大小
    const touchTargets = document.querySelectorAll(
      'button, a, input[type="button"], input[type="submit"]'
    );
    let smallTargets = 0;
    touchTargets.forEach((target) => {
      const rect = target.getBoundingClientRect();
      if (rect.width < 44 || rect.height < 44) {
        smallTargets++;
      }
    });

    if (smallTargets > 0) {
      issues.push(`${smallTargets} 个触摸目标过小`);
      recommendations.push('确保触摸目标至少为 44x44 像素');
    }

    // 检查文本可读性
    const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6');
    let smallText = 0;
    textElements.forEach((element) => {
      const style = window.getComputedStyle(element);
      const fontSize = parseInt(style.fontSize);
      if (fontSize < 16) {
        smallText++;
      }
    });

    if (smallText > textElements.length * 0.5) {
      issues.push('文本过小，影响可读性');
      recommendations.push('确保主要文本至少为 16px');
    }

    // 检查水平滚动
    if (document.body.scrollWidth > window.innerWidth) {
      issues.push('页面需要水平滚动');
      recommendations.push('确保内容适应屏幕宽度');
    }

    const passed = issues.length === 0;

    return {
      passed,
      issues,
      recommendations,
    };
  }

  /**
   * 获取移动端性能指标
   */
  async getMobilePerformanceMetrics(): Promise<MobilePerformanceMetrics | null> {
    if (typeof window === 'undefined') return null;

    try {
      const { onCLS, onINP, onFCP, onLCP, onTTFB } = await import('web-vitals');

      return new Promise((resolve) => {
        const metrics: Partial<MobilePerformanceMetrics> = {};
        let metricsReceived = 0;
        const totalMetrics = 5;

        const checkComplete = () => {
          metricsReceived++;
          if (metricsReceived === totalMetrics) {
            resolve(metrics as MobilePerformanceMetrics);
          }
        };

        onCLS((metric: any) => {
          metrics.cumulativeLayoutShift = metric.value;
          checkComplete();
        });

        onINP((metric: any) => {
          metrics.firstInputDelay = metric.value;
          checkComplete();
        });

        onFCP((metric: any) => {
          metrics.firstContentfulPaint = metric.value;
          checkComplete();
        });

        onLCP((metric: any) => {
          metrics.largestContentfulPaint = metric.value;
          checkComplete();
        });

        onTTFB((metric: any) => {
          // 使用 TTFB 作为 TTI 的近似值
          metrics.timeToInteractive = metric.value;
          checkComplete();
        });

        // 设置超时
        setTimeout(() => {
          resolve(metrics as MobilePerformanceMetrics);
        }, 5000);
      });
    } catch (error) {
      console.error('Failed to get mobile performance metrics:', error);
      return null;
    }
  }

  /**
   * 初始化移动端优化
   */
  initialize(): void {
    if (typeof window === 'undefined') return;

    const device = this.detectMobileDevice();

    if (device.isMobile || device.isTablet) {
      this.optimizeTouchExperience();
      this.optimizeMobilePerformance();

      console.log('Mobile SEO optimizations applied for', device.deviceType);
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<MobileOptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// 导出单例实例
export const mobileSEOOptimizer = new MobileSEOOptimizer();
