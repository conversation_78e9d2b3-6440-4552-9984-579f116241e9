# TradingAgents 完整 CI/CD 部署指南

## 概述

本项目现在支持前端和后端服务的完整 CI/CD 部署流程，包括：

- **前端服务**: Next.js 应用 (端口 3000)
- **后端服务**: AKShare 数据服务 (端口 5000)
- **数据库服务**: MySQL 8.0 (端口 13306)

## CI/CD 流程

### 自动触发条件

1. **代码推送**: 推送到 `main` 或 `develop` 分支
2. **Pull Request**: 创建或更新 PR
3. **手动触发**: 通过 GitHub Actions 界面手动运行

### 构建阶段

#### 1. 代码质量检查
- **前端**: ESLint、TypeScript 类型检查
- **后端**: Black、isort、Flake8、MyPy

#### 2. Docker 镜像构建
- **前端镜像**: `crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend`
- **后端镜像**: `crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/akshare-backend`

#### 3. 安全扫描
- 使用 Trivy 对 Docker 镜像进行安全漏洞扫描

#### 4. 镜像测试
- 测试前端和后端容器的启动和健康检查

### 部署阶段

#### 部署环境
- **生产环境**: `main` 分支自动部署
- **测试环境**: `develop` 分支自动部署

#### 部署选项
通过手动触发可以选择部署的服务：
- `all`: 部署前端、后端和数据库 (默认)
- `frontend`: 仅部署前端服务
- `backend`: 仅部署后端服务

## Docker Compose 配置

### 完整部署 (`docker-compose.prod.yml`)
```yaml
services:
  - akshare-backend (后端API服务)
  - frontend (前端Web应用)
  - mysql (数据库服务)
  - nginx (反向代理，可选)
```

### 前端独立部署 (`docker-compose.prod.frontend.yml`)
```yaml
services:
  - frontend (前端Web应用)
  - mysql (数据库服务)
  - nginx (反向代理，可选)
```

### 后端独立部署 (`docker-compose.prod.backend.yml`)
```yaml
services:
  - akshare-backend (后端API服务)
  - mysql (数据库服务)
```

## 环境变量配置

### 必需的 GitHub Secrets

#### 阿里云镜像仓库
```
ALIYUN_REGISTRY_USERNAME=your_username
ALIYUN_REGISTRY_PASSWORD=your_password
```

#### 部署服务器
```
DEPLOY_HOST=your_server_ip
DEPLOY_USER=your_username
DEPLOY_SSH_KEY=your_private_key
DEPLOY_PATH=/root
DEPLOY_URL=http://your_domain:3000
API_URL=http://your_domain:5000
```

#### 数据库配置
```
MYSQL_ROOT_PASSWORD=your_root_password
MYSQL_PASSWORD=your_user_password
```

#### API 密钥
```
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_key
NEXT_PUBLIC_FINNHUB_API_KEY=your_finnhub_key
NEXT_PUBLIC_WS_URL=ws://your_domain:8000
```

### 生产环境变量 (`.env.production`)
```env
NODE_ENV=production
NEXT_PUBLIC_API_BASE_URL=http://your_domain:5000
NEXT_PUBLIC_API_BACKEND_BASE_URL=http://your_domain:5000
AKSHARE_API_URL=http://your_domain:5000
MYSQL_DATABASE=trading_analysis
MYSQL_USER=trading_user
```

## 部署流程

### 1. 自动部署
推送代码到 `main` 或 `develop` 分支会自动触发部署。

### 2. 手动部署
1. 访问 GitHub Actions 页面
2. 选择 "CI/CD Pipeline" 工作流
3. 点击 "Run workflow"
4. 选择部署参数：
   - 部署环境 (staging/production)
   - 部署服务 (all/frontend/backend)
   - 是否强制部署
   - 是否推送镜像

### 3. 部署验证
部署完成后会自动进行健康检查：
- 前端服务: HTTP 200 响应检查
- 后端服务: `/health` 端点检查
- 容器状态: Docker 容器运行状态检查

## 服务访问

### 前端应用
- **URL**: `http://your_domain:3000`
- **功能**: 交易分析界面、任务管理、实时数据展示

### 后端API
- **URL**: `http://your_domain:5000`
- **健康检查**: `http://your_domain:5000/health`
- **API文档**: `http://your_domain:5000/docs`
- **功能**: AKShare 数据接口、股票数据、技术指标

### 数据库
- **端口**: `13306`
- **数据库**: `trading_analysis`
- **用户**: `trading_user`

## 监控和日志

### 容器健康检查
- **前端**: 30秒间隔 HTTP 检查
- **后端**: 30秒间隔 `/health` 端点检查
- **数据库**: 30秒间隔 `mysqladmin ping` 检查

### 日志管理
- 日志文件大小限制: 10MB
- 保留日志文件数: 3个
- 日志格式: JSON

### 查看日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.prod.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml logs -f frontend
docker-compose -f docker-compose.prod.yml logs -f akshare-backend
```

## 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 检查容器状态
docker-compose ps

# 查看容器日志
docker-compose logs [service_name]

# 重启服务
docker-compose restart [service_name]
```

#### 2. 网络连接问题
```bash
# 检查网络
docker network ls
docker network inspect tradingagents-network

# 测试服务连通性
docker exec -it tradingagents-frontend curl http://akshare-backend:5000/health
```

#### 3. 数据库连接问题
```bash
# 检查数据库状态
docker exec -it tradingagents-mysql mysqladmin ping

# 连接数据库
docker exec -it tradingagents-mysql mysql -u trading_user -p trading_analysis
```

### 回滚部署
如果部署出现问题，可以快速回滚到之前的版本：

```bash
# 停止当前服务
docker-compose down

# 拉取之前的镜像版本
docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:previous_tag
docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/akshare-backend:previous_tag

# 更新 docker-compose.yml 中的镜像标签
# 重新启动服务
docker-compose up -d
```

## 性能优化

### 镜像优化
- 使用多阶段构建减小镜像大小
- 利用 Docker 层缓存加速构建
- 使用 `.dockerignore` 排除不必要文件

### 部署优化
- 并行构建前端和后端镜像
- 使用 GitHub Actions 缓存
- 分阶段部署减少停机时间

### 资源配置
根据服务器资源调整容器资源限制：

```yaml
services:
  frontend:
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
  
  akshare-backend:
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

## 安全考虑

### 镜像安全
- 定期更新基础镜像
- 使用非 root 用户运行容器
- 定期进行安全扫描

### 网络安全
- 使用内部网络进行服务通信
- 限制对外暴露的端口
- 配置防火墙规则

### 数据安全
- 数据库密码使用强密码
- 定期备份数据
- 加密敏感环境变量

## 扩展和维护

### 添加新服务
1. 在相应的 `docker-compose.yml` 文件中添加服务定义
2. 更新 CI/CD 流程以包含新服务的构建和部署
3. 添加健康检查和监控

### 更新依赖
1. 定期更新 `package.json` 和 `requirements.txt`
2. 测试兼容性
3. 更新 Docker 基础镜像

### 监控和告警
考虑集成监控工具：
- Prometheus + Grafana
- ELK Stack (Elasticsearch, Logstash, Kibana)
- 云监控服务 (阿里云监控、AWS CloudWatch)