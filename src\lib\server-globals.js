/**
 * 服务器端全局变量的mock实现
 * 用于防止在服务器端渲染时出现 'self is not defined' 等错误
 */

// 创建一个基础的mock对象
const createMockObject = (name) => {
  return new Proxy({}, {
    get: (target, prop) => {
      // 对于常见的方法，返回空函数
      if (typeof prop === 'string' && (
        prop.includes('add') || 
        prop.includes('remove') || 
        prop.includes('set') || 
        prop.includes('get') ||
        prop === 'toString' ||
        prop === 'valueOf'
      )) {
        return () => {};
      }
      
      // 对于其他属性，返回undefined或空对象
      return undefined;
    },
    set: () => true, // 允许设置任何属性
    has: () => true, // 假装所有属性都存在
  });
};

// 创建更完整的window mock
const mockWindow = {
  // 基础属性
  location: {
    href: '',
    origin: 'http://localhost:3000',
    protocol: 'http:',
    host: 'localhost:3000',
    hostname: 'localhost',
    port: '3000',
    pathname: '/',
    search: '',
    hash: '',
    assign: () => {},
    replace: () => {},
    reload: () => {},
  },
  
  // 历史API
  history: {
    length: 1,
    state: null,
    pushState: () => {},
    replaceState: () => {},
    back: () => {},
    forward: () => {},
    go: () => {},
  },
  
  // 存储API
  localStorage: {
    length: 0,
    getItem: () => null,
    setItem: () => {},
    removeItem: () => {},
    clear: () => {},
    key: () => null,
  },
  
  sessionStorage: {
    length: 0,
    getItem: () => null,
    setItem: () => {},
    removeItem: () => {},
    clear: () => {},
    key: () => null,
  },
  
  // 加密API
  crypto: {
    getRandomValues: (arr) => {
      if (arr && arr.length) {
        for (let i = 0; i < arr.length; i++) {
          arr[i] = Math.floor(Math.random() * 256);
        }
      }
      return arr;
    },
    randomUUID: () => {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },
  },
  
  // 性能API
  performance: {
    now: () => Date.now(),
    mark: () => {},
    measure: () => {},
    getEntriesByType: () => [],
    getEntriesByName: () => [],
  },
  
  // 动画API
  requestAnimationFrame: (callback) => {
    return setTimeout(() => callback(Date.now()), 16);
  },
  
  cancelAnimationFrame: (id) => {
    clearTimeout(id);
  },
  
  // 事件API
  addEventListener: () => {},
  removeEventListener: () => {},
  dispatchEvent: () => false,
  
  // 尺寸属性
  innerWidth: 1024,
  innerHeight: 768,
  outerWidth: 1024,
  outerHeight: 768,
  screen: {
    width: 1024,
    height: 768,
    availWidth: 1024,
    availHeight: 768,
  },
  
  // 其他常用属性
  navigator: {
    userAgent: 'Mozilla/5.0 (Node.js)',
    language: 'zh-CN',
    languages: ['zh-CN', 'zh', 'en'],
    platform: 'Node.js',
    onLine: true,
    cookieEnabled: false,
  },
  
  // 控制台
  console: console,
  
  // 定时器
  setTimeout: setTimeout,
  clearTimeout: clearTimeout,
  setInterval: setInterval,
  clearInterval: clearInterval,
  
  // 基础方法
  toString: () => '[object Window]',
  valueOf: () => null,
};

// 创建document mock
const mockDocument = {
  createElement: (tagName) => ({
    tagName: tagName.toUpperCase(),
    setAttribute: () => {},
    getAttribute: () => null,
    appendChild: () => {},
    removeChild: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    style: {},
    textContent: '',
    innerHTML: '',
  }),
  
  querySelector: () => null,
  querySelectorAll: () => [],
  getElementById: () => null,
  getElementsByTagName: () => [],
  getElementsByClassName: () => [],
  
  head: {
    appendChild: () => {},
    removeChild: () => {},
  },
  
  body: {
    appendChild: () => {},
    removeChild: () => {},
    style: {},
  },
  
  title: '',
  cookie: '',
  
  addEventListener: () => {},
  removeEventListener: () => {},
  
  toString: () => '[object Document]',
};

// 导出所有需要的全局变量
module.exports = {
  self: mockWindow,
  window: mockWindow,
  document: mockDocument,
  navigator: mockWindow.navigator,
  location: mockWindow.location,
  localStorage: mockWindow.localStorage,
  sessionStorage: mockWindow.sessionStorage,
  crypto: mockWindow.crypto,
  performance: mockWindow.performance,
  requestAnimationFrame: mockWindow.requestAnimationFrame,
  cancelAnimationFrame: mockWindow.cancelAnimationFrame,
};
