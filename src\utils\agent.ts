// 注意：LangGraph相关功能已移至后端
// 前端通过API调用后端服务，不再直接使用LangGraph库

// import { createReactAgent } from '@langchain/langgraph/prebuilt';
// import { ClientConfig } from '@langchain/mcp-adapters';
// import { ChatOpenAI } from '@langchain/openai';

// 临时禁用，所有AI功能通过后端API实现
const askAI = async (
  config: any, // Partial<ClientConfig>,
  question: string
): Promise<{ content: string; agent: any }> => {
  // 所有LangGraph功能已移至后端
  // 前端应该通过API调用后端服务

  console.log('askAI 已禁用，请使用后端API服务');

  // 返回一个占位符响应
  return {
    content: '此功能已移至后端，请使用相应的API端点',
    agent: null,
  };

  /*
  // 原始代码已注释，等待完全移除
  // Create client and connect to server
  //   const client = new MultiServerMCPClient({...});
  //   const tools = await client.getTools();

  // Create an OpenAI model
  const model = new ChatOpenAI({
    modelName: 'gpt-4.1-2025-04-14',
    configuration: {
      baseURL: process.env.OPENAI_API_BASE || 'https://api.nuwaapi.com',
    },
    apiKey: process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY,
  });

  // Create the React agent
  const agent = createReactAgent({
    llm: model,
    tools: [], // Add tools if needed
  });

  let res;
  // Run the agent
  console.log(question);
  try {
    res = await agent.invoke({
      messages: [{ role: 'user', content: question }],
    });
    console.log('Agent response:', res);
  } catch (error: any) {
    console.error('Error during agent execution:', error);
    // Tools throw ToolException for tool-specific errors
    if (error.name === 'ToolException') {
      console.error('Tool execution failed:', error.message);
    }
  } finally {
    // Close the client connection
    // await client.close();
    return {
      content: res?.messages?.[1]?.content as string,
      agent,
    };
  }
  */
};

export default askAI;
