// LangGraph Workflow Database Operations - V4.0 (Workflow-centric)
import mysql from 'mysql2/promise';
import {
  // Keep necessary request/response types, but many will need to be updated/refactored
  // This requires a corresponding update in `src/types/langgraph-database.ts`
  // For now, we assume they are updated.
  CreateWorkflowRequest,
  LogWorkflowEventRequest,
  SaveAnalystReportRequest, // Renamed and redesigned
  SaveFinalDecisionRequest, // Renamed and redesigned
  SaveResearchReportRequest, // Renamed and redesigned
  SaveStateSnapshotRequest,
  UpdateWorkflowStatusRequest,
  Workflow, // New type
  WorkflowQueryOptions,
} from '../types/langgraph-database';

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'trading_analysis',
  charset: 'utf8mb4',
  timezone: '+00:00',
};

// Create a connection pool
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

export class LangGraphDatabase {
  // ============================================================================
  // Private Utility Methods
  // ============================================================================

  protected static async withConnection<T>(
    operation: (connection: mysql.PoolConnection) => Promise<T>
  ): Promise<T> {
    const connection = await pool.getConnection();
    try {
      return await operation(connection);
    } finally {
      connection.release();
    }
  }

  protected static generateId(prefix: string): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  private static safeJsonStringify(obj: any): string | null {
    if (obj === undefined || obj === null) {
      return null;
    }
    try {
      return JSON.stringify(obj);
    } catch (error) {
      console.error('Failed to stringify object:', error);
      return null;
    }
  }

  // ============================================================================
  // 1. Workflow Instance Management
  // ============================================================================

  static async createWorkflow(request: CreateWorkflowRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      const workflowId = this.generateId('wf');
      await connection.execute('CALL CreateWorkflow(?, ?, ?, ?, ?, ?)', [
        workflowId,
        request.ticker || '',
        request.title || '',
        request.description || null,
        this.safeJsonStringify(request.config) || null,
        request.created_by || 'system',
      ]);
      return workflowId;
    });
  }

  static async updateWorkflowStatus(request: UpdateWorkflowStatusRequest): Promise<void> {
    await this.withConnection(async (connection) => {
      await connection.execute('CALL UpdateWorkflowStatus(?, ?, ?, ?, ?)', [
        request.workflow_id || '',
        request.current_stage || '',
        request.progress || 0,
        request.status || 'pending',
        request.error_message || null,
      ]);
    });
  }

  static async getWorkflow(workflow_id: string): Promise<Workflow | null> {
    return this.withConnection(async (connection) => {
      const [rows] = await connection.execute('SELECT * FROM workflows WHERE workflow_id = ?', [
        workflow_id,
      ]);
      const results = rows as Workflow[];
      return results.length > 0 ? results[0] : null;
    });
  }

  static async queryWorkflows(options: WorkflowQueryOptions = {}): Promise<Workflow[]> {
    return this.withConnection(async (connection) => {
      let query = 'SELECT * FROM workflow_overview WHERE 1=1';
      const params: any[] = [];

      if (options.ticker) {
        query += ' AND ticker = ?';
        params.push(options.ticker);
      }
      if (options.status && options.status.length > 0) {
        query += ` AND status IN (${options.status.map(() => '?').join(',')})`;
        params.push(...options.status);
      }
      if (options.date_from) {
        query += ' AND created_at >= ?';
        params.push(options.date_from);
      }
      if (options.date_to) {
        query += ' AND created_at <= ?';
        params.push(options.date_to);
      }

      query += ' ORDER BY created_at DESC';

      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);
      }
      if (options.offset) {
        query += ' OFFSET ?';
        params.push(options.offset);
      }

      const [rows] = await connection.execute(query, params);
      return rows as Workflow[];
    });
  }

  // ============================================================================
  // 2. Analyst & Research Report Management
  // ============================================================================

  static async saveAnalystReport(request: SaveAnalystReportRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      await connection.beginTransaction();
      try {
        const reportId = this.generateId('rep');
        // 1. Insert into the main analyst_reports table
        const reportSql = `
          INSERT INTO analyst_reports (report_id, workflow_id, analyst_type, summary, status, execution_time_ms)
          VALUES (?, ?, ?, ?, ?, ?)
        `;
        await connection.execute(reportSql, [
          reportId,
          request.workflow_id,
          request.analyst_type,
          request.summary || null,
          request.status || 'completed',
          request.execution_time_ms || null,
        ]);

        // 2. Insert into the specific details table based on type
        let detailsSql = '';
        let detailsParams: any[] = [];

        switch (request.analyst_type) {
          case 'technical':
            const techDetails = request.details as any;
            detailsSql = `
              INSERT INTO technical_analysis_details (report_id, trading_signal, trend_signal, support_level, resistance_level, stop_loss_level, target_price, rsi_value, macd_signal, key_levels)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;
            detailsParams = [
              reportId,
              techDetails?.trading_signal || null,
              techDetails?.trend_signal || null,
              techDetails?.support_level || null,
              techDetails?.resistance_level || null,
              techDetails?.stop_loss_level || null,
              techDetails?.target_price || null,
              techDetails?.rsi_value || null,
              techDetails?.macd_signal || null,
              this.safeJsonStringify(techDetails?.key_levels) || null,
            ];
            break;
          case 'sentiment':
            const sentDetails = request.details as any;
            detailsSql = `
              INSERT INTO sentiment_analysis_details (report_id, overall_sentiment, sentiment_score, positive_news_count, negative_news_count, neutral_news_count, key_drivers)
              VALUES (?, ?, ?, ?, ?, ?, ?)
            `;
            detailsParams = [
              reportId,
              sentDetails?.overall_sentiment || 'neutral',
              sentDetails?.sentiment_score || 0,
              sentDetails?.positive_news_count || 0,
              sentDetails?.negative_news_count || 0,
              sentDetails?.neutral_news_count || 0,
              sentDetails?.key_drivers || null,
            ];
            break;
          // Add cases for 'news' and 'fundamental' if they have detail tables
        }

        if (detailsSql) {
          await connection.execute(detailsSql, detailsParams);
        }

        await connection.commit();
        return reportId;
      } catch (error) {
        await connection.rollback();
        console.error(`Failed to save analyst report for workflow ${request.workflow_id}:`, error);
        throw error;
      }
    });
  }

  static async saveResearchReport(request: SaveResearchReportRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      await connection.beginTransaction();
      try {
        const reportId = this.generateId('res');
        // 1. Insert into the main research_reports table
        const reportSql = `
          INSERT INTO research_reports (report_id, workflow_id, researcher_type, summary, confidence_level, target_price, time_horizon, status)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `;
        await connection.execute(reportSql, [
          reportId,
          request.workflow_id,
          request.researcher_type,
          request.summary || null,
          request.confidence_level || null,
          request.target_price || null,
          request.time_horizon || null,
          request.status || 'completed',
        ]);

        // 2. Insert arguments into research_arguments table
        if (request.arguments && request.arguments.length > 0) {
          const argumentSql = `
            INSERT INTO research_arguments (argument_id, report_id, parent_argument_id, argument_type, content, source, strength_score, sequence_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `;
          for (const arg of request.arguments) {
            const argumentId = this.generateId('arg');
            await connection.execute(argumentSql, [
              argumentId,
              reportId,
              arg.parent_argument_id || null,
              arg.argument_type || 'main_argument',
              arg.content || '',
              arg.source || null,
              arg.strength_score || null,
              arg.sequence_order || 0,
            ]);
          }
        }

        await connection.commit();
        return reportId;
      } catch (error) {
        await connection.rollback();
        console.error(`Failed to save research report for workflow ${request.workflow_id}:`, error);
        throw error;
      }
    });
  }

  // ... Methods to get analyst and research reports would be added here ...
  // These would involve JOINs between the main and details tables.

  // ============================================================================
  // 3. Debate and Decision Management
  // ============================================================================

  static async saveFinalDecision(request: SaveFinalDecisionRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      const decisionId = this.generateId('dec');
      const sql = `
        INSERT INTO final_decisions (decision_id, workflow_id, decision_type, confidence_level, decision_rationale, entry_price_range, stop_loss_price, take_profit_price, position_size_percentage)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      await connection.execute(sql, [
        decisionId,
        request.workflow_id,
        request.decision_type || 'hold',
        request.confidence_level || null,
        request.decision_rationale || null,
        this.safeJsonStringify(request.entry_price_range) || null,
        request.stop_loss_price || null,
        request.take_profit_price || null,
        request.position_size_percentage || null,
      ]);
      return decisionId;
    });
  }

  // ... Methods for saving and getting debate sessions, rounds, and utterances ...

  // ============================================================================
  // 4. Event Logging and State Snapshots
  // ============================================================================

  static async logWorkflowEvent(request: LogWorkflowEventRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      const eventId = this.generateId('evt');
      await connection.execute('CALL LogWorkflowEvent(?, ?, ?, ?, ?, ?)', [
        eventId,
        request.workflow_id || '',
        request.stage_name || null,
        request.event_type || 'log',
        request.content || '',
        this.safeJsonStringify(request.metadata) || null,
      ]);
      return eventId;
    });
  }

  static async saveStateSnapshot(request: SaveStateSnapshotRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      const snapshotId = this.generateId('snap');
      const sql = `
        INSERT INTO workflow_state_snapshots (snapshot_id, workflow_id, stage_name, state_data, checkpoint_id)
        VALUES (?, ?, ?, ?, ?)
      `;
      await connection.execute(sql, [
        snapshotId,
        request.workflow_id,
        request.stage_name,
        this.safeJsonStringify(request.state_data) || null,
        request.checkpoint_id || null,
      ]);
      return snapshotId;
    });
  }

  // ============================================================================
  // 5. System and Cleanup
  // ============================================================================

  static async closePool(): Promise<void> {
    await pool.end();
  }
}

export default LangGraphDatabase;
