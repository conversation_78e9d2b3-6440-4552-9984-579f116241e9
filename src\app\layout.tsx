/*
 * @Author: ezrealbb <EMAIL>
 * @Date: 2025-07-04 21:56:55
 * @LastEditors: ezrealbb <EMAIL>
 * @LastEditTime: 2025-08-13 21:19:49
 * @FilePath: \trading-agents-frontend\src\app\layout.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import { NetworkStatus } from '@/components/common/ConnectionStatus';
import { DevTools } from '@/components/common/DevTools';
import { EnvLogger } from '@/components/common/EnvLogger';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { Footer } from '@/components/layout/Footer';
import { Header } from '@/components/layout/Header';
import { Providers } from '@/components/providers';
import { RootStructuredData } from '@/components/seo/StructuredDataScript';
import { PerformanceMonitor } from '@/components/seo/PerformanceMonitor';
import {
  MobileOptimizer,
  MobileViewport,
  ResponsiveValidator,
  TouchTargetValidator,
} from '@/components/seo/MobileOptimizer';
import { logEnvironmentVariables } from '@/utils/env-logger';

import { createSSROptimizer } from '@/lib/seo/ssr-optimization';
import { createPerformanceOptimizer } from '@/lib/seo/performance-optimization';
import type { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import { Toaster } from 'react-hot-toast';
import './globals.css';

// 在服务端启动时打印环境变量
if (typeof window === 'undefined') {
  logEnvironmentVariables();
}

const inter = Inter({ subsets: ['latin'] });

// 生成根布局的 SEO 元数据
async function generateRootMetadata(): Promise<Metadata> {
  const ssrOptimizer = createSSROptimizer();

  const optimizedMetadata = await ssrOptimizer.generateOptimizedMetadata({
    page: 'home',
    locale: 'zh',
  });

  // 添加根布局特有的配置
  return {
    ...optimizedMetadata,
    icons: {
      icon: '/tradingAgent.ico',
      shortcut: '/tradingAgent.ico',
      apple: '/tradingAgent.png',
      other: [
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '32x32',
          url: '/tradingAgent.png',
        },
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '16x16',
          url: '/tradingAgent.ico',
        },
      ],
    },
    manifest: '/manifest.json',
    other: {
      'msapplication-TileColor': '#000000',
      'theme-color': '#000000',
    },
  };
}

export async function generateMetadata(): Promise<Metadata> {
  return await generateRootMetadata();
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const performanceOptimizer = createPerformanceOptimizer();
  const ssrOptimizer = createSSROptimizer();

  // 生成关键资源预加载
  const criticalPreloads = ssrOptimizer.generateCriticalCSS('home');
  const resourceHints = performanceOptimizer.generateResourceHints();

  return (
    <html lang="zh-CN">
      <head>
        {/* 结构化数据 */}
        <RootStructuredData />

        {/* 关键 CSS 内联 */}
        {criticalPreloads && <style dangerouslySetInnerHTML={{ __html: criticalPreloads }} />}

        {/* 预加载关键资源 */}
        <link rel="preload" href="/tradingAgent.png" as="image" type="image/png" />
        <link
          rel="preload"
          href="/fonts/inter-var.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />

        {/* 资源提示 */}
        {resourceHints.map((hint, index) => (
          <link
            key={index}
            rel="dns-prefetch"
            href={hint.replace('<link rel="dns-prefetch" href="', '').replace('">', '')}
          />
        ))}

        {/* 预连接关键域名 */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* 安全头 */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta name="referrer" content="strict-origin-when-cross-origin" />

        {/* PWA 支持 */}
        <meta name="application-name" content="TradingAgents" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="TradingAgents" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <meta name="msapplication-TileColor" content="#000000" />
        <meta name="theme-color" content="#000000" />

        {/* 性能监控脚本 */}
        <script
          dangerouslySetInnerHTML={{
            __html: performanceOptimizer.generatePerformanceMonitoringScript(),
          }}
        />
      </head>
      <body className={inter.className}>
        <ErrorBoundary>
          <Providers>
            <EnvLogger />
            <DevTools />

            {/* 跳转链接 - 仅对屏幕阅读器可见 */}
            <nav className="sr-only focus-within:not-sr-only" aria-label="跳转链接">
              <ul className="flex space-x-4 p-4 bg-blue-600 text-white">
                <li>
                  <a
                    href="#main-content"
                    className="underline hover:no-underline focus:outline-none focus:ring-2 focus:ring-white"
                  >
                    跳转到主要内容
                  </a>
                </li>
                <li>
                  <a
                    href="#main-navigation"
                    className="underline hover:no-underline focus:outline-none focus:ring-2 focus:ring-white"
                  >
                    跳转到导航菜单
                  </a>
                </li>
              </ul>
            </nav>

            <div className="min-h-screen flex flex-col">
              {/* 语义化头部导航 */}
              <header role="banner">
                <Header />
              </header>

              {/* 网络状态指示器 */}
              <aside role="complementary" aria-label="网络连接状态">
                <NetworkStatus />
              </aside>

              {/* 主要内容区域 */}
              <main id="main-content" role="main" className="flex-1 pt-16" aria-label="主要内容">
                {children}
              </main>

              {/* 语义化页脚 */}
              <footer role="contentinfo">
                <Footer />
              </footer>
            </div>

            {/* 性能监控 */}
            <PerformanceMonitor />

            {/* 移动端优化 */}
            <MobileViewport />
            <MobileOptimizer />
            <ResponsiveValidator />
            <TouchTargetValidator />

            {/* 通知系统 */}
            <div role="region" aria-label="通知消息" aria-live="polite">
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: '#363636',
                    color: '#fff',
                  },
                  success: {
                    duration: 3000,
                    iconTheme: {
                      primary: '#22c55e',
                      secondary: '#fff',
                    },
                  },
                  error: {
                    duration: 5000,
                    iconTheme: {
                      primary: '#ef4444',
                      secondary: '#fff',
                    },
                  },
                }}
              />
            </div>
          </Providers>
        </ErrorBoundary>
      </body>
    </html>
  );
}
