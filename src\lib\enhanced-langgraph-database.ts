// Enhanced LangGraph Database Operations - Extended for Analysis Data Storage
// Extends the existing LangGraphDatabase with complete workflow status queries,
// batch operations, and analysis statistics functionality

import mysql from 'mysql2/promise';
import {
  AnalystReport,
  AnalystType,
  CreateWorkflowRequest,
  FinalDecision,
  ResearchReport,
  SaveAnalystReportRequest,
  Workflow,
  WorkflowEvent,
} from '../types/langgraph-database';
import LangGraphDatabase from './langgraph-database';

// ============================================================================
// Extended Types for Enhanced Functionality
// ============================================================================

export interface CompleteWorkflowStatus {
  workflow: Workflow;
  analystReports: AnalystReport[];
  researchReports: ResearchReport[];
  finalDecision: FinalDecision | null;
  recentEvents: WorkflowEvent[];
}

export interface CreateCompleteWorkflowRequest extends CreateWorkflowRequest {
  config?: any;
}

export interface AgentStateUpdate {
  workflow_id: string;
  analyst_type: string;
  status: 'completed' | 'failed';
  execution_time_ms?: number;
}

export interface StatisticsOptions {
  date_from?: string;
  date_to?: string;
  ticker?: string;
}

export interface AnalysisStatistics {
  basic: {
    total_workflows: number;
    completed_workflows: number;
    failed_workflows: number;
    avg_duration_seconds: number;
  };
  successRates: Array<{
    ticker: string;
    total_count: number;
    success_count: number;
  }>;
}

export interface WorkflowStatusSummary {
  workflow_id: string;
  status: string;
  progress: number;
  current_stage: string;
  analyst_completion: {
    fundamental: boolean;
    technical: boolean;
    sentiment: boolean;
    news: boolean;
  };
  research_completion: {
    bull: boolean;
    bear: boolean;
  };
  final_decision_completed: boolean;
}

// ============================================================================
// Enhanced LangGraph Database Class
// ============================================================================

export class EnhancedLangGraphDatabase extends LangGraphDatabase {
  // ============================================================================
  // Enhanced Workflow Management
  // ============================================================================

  /**
   * Creates a complete workflow with all necessary initialization
   * Requirements: 需求 1.1, 需求 7.1
   */
  static async createCompleteWorkflow(request: CreateCompleteWorkflowRequest): Promise<string> {
    // Use a direct database connection since we can't access the private method
    const mysql = await import('mysql2/promise');
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'trading_analysis',
      charset: 'utf8mb4',
      timezone: '+00:00',
    };

    const connection = await mysql.createConnection(dbConfig);

    try {
      await connection.beginTransaction();

      // 1. Create the main workflow
      const workflowId = await this.createWorkflow(request);

      // 2. Initialize analyst report placeholders
      await this.initializeAnalystStates(connection, workflowId, request.config);

      // 3. Log initial event
      await this.logWorkflowEvent({
        workflow_id: workflowId,
        stage_name: 'initialization',
        event_type: 'log',
        content: '工作流初始化完成',
        metadata: { config: request.config },
      });

      await connection.commit();
      return workflowId;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.end();
    }
  }

  /**
   * Initialize analyst states for a workflow
   * Requirements: 需求 1.1, 需求 1.2
   */
  private static async initializeAnalystStates(
    connection: mysql.Connection,
    workflowId: string,
    config?: any
  ): Promise<void> {
    const analystTypes: AnalystType[] = ['fundamental', 'technical', 'sentiment', 'news'];

    for (const analystType of analystTypes) {
      const reportId = this.generateUniqueId('rep');
      await connection.execute(
        `INSERT INTO analyst_reports (report_id, workflow_id, analyst_type, summary, status, execution_time_ms)
         VALUES (?, ?, ?, ?, ?, ?)`,
        [reportId, workflowId, analystType, null, 'pending', null]
      );
    }
  }

  /**
   * Get complete workflow status with all related data
   * Requirements: 需求 1.3, 需求 2.1, 需求 2.2
   */
  static async getCompleteWorkflowStatus(workflow_id: string): Promise<CompleteWorkflowStatus> {
    const mysql = await import('mysql2/promise');
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'trading_analysis',
      charset: 'utf8mb4',
      timezone: '+00:00',
    };

    const connection = await mysql.createConnection(dbConfig);

    try {
      // Get workflow basic information
      const workflow = await this.getWorkflow(workflow_id);
      if (!workflow) {
        throw new Error(`工作流不存在: ${workflow_id}`);
      }

      // Get all analyst reports with details
      const [analystReports] = await connection.execute(
        `SELECT ar.*, 
                tad.trading_signal, tad.trend_signal, tad.support_level, tad.resistance_level,
                tad.stop_loss_level, tad.target_price, tad.rsi_value, tad.macd_signal, tad.key_levels,
                sad.overall_sentiment, sad.sentiment_score, sad.positive_news_count, 
                sad.negative_news_count, sad.neutral_news_count, sad.key_drivers
         FROM analyst_reports ar
         LEFT JOIN technical_analysis_details tad ON ar.report_id = tad.report_id
         LEFT JOIN sentiment_analysis_details sad ON ar.report_id = sad.report_id
         WHERE ar.workflow_id = ? 
         ORDER BY ar.created_at`,
        [workflow_id]
      );

      // Get all research reports with arguments
      const [researchReports] = await connection.execute(
        `SELECT rr.*, 
                GROUP_CONCAT(
                  CONCAT(ra.argument_type, ':', ra.content, '|', COALESCE(ra.strength_score, 0))
                  ORDER BY ra.sequence_order SEPARATOR ';;'
                ) as arguments_data
         FROM research_reports rr
         LEFT JOIN research_arguments ra ON rr.report_id = ra.report_id
         WHERE rr.workflow_id = ?
         GROUP BY rr.report_id
         ORDER BY rr.created_at`,
        [workflow_id]
      );

      // Get final decision
      const [decisions] = await connection.execute(
        'SELECT * FROM final_decisions WHERE workflow_id = ? ORDER BY created_at DESC LIMIT 1',
        [workflow_id]
      );

      // Get recent events
      const [events] = await connection.execute(
        'SELECT * FROM workflow_events WHERE workflow_id = ? ORDER BY created_at DESC LIMIT 50',
        [workflow_id]
      );

      const decisionsArray = decisions as FinalDecision[];
      const eventsArray = events as WorkflowEvent[];

      return {
        workflow,
        analystReports: analystReports as AnalystReport[],
        researchReports: researchReports as ResearchReport[],
        finalDecision: decisionsArray.length > 0 ? decisionsArray[0] : null,
        recentEvents: eventsArray,
      };
    } finally {
      await connection.end();
    }
  }

  // ============================================================================
  // Batch Operations
  // ============================================================================

  /**
   * Batch update agent states
   * Requirements: 需求 1.2, 需求 7.2
   */
  static async batchUpdateAgentStates(updates: AgentStateUpdate[]): Promise<void> {
    const mysql = await import('mysql2/promise');
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'trading_analysis',
      charset: 'utf8mb4',
      timezone: '+00:00',
    };

    const connection = await mysql.createConnection(dbConfig);

    try {
      await connection.beginTransaction();

      for (const update of updates) {
        await connection.execute(
          `UPDATE analyst_reports 
           SET status = ?, execution_time_ms = ?, updated_at = NOW()
           WHERE workflow_id = ? AND analyst_type = ?`,
          [update.status, update.execution_time_ms, update.workflow_id, update.analyst_type]
        );
      }

      await connection.commit();
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.end();
    }
  }

  /**
   * Batch save analyst results with details
   * Requirements: 需求 1.2, 需求 1.3
   */
  static async batchSaveAnalystResults(results: SaveAnalystReportRequest[]): Promise<string[]> {
    const reportIds: string[] = [];

    for (const result of results) {
      const reportId = await this.saveAnalystReport(result);
      reportIds.push(reportId);
    }

    return reportIds;
  }

  // ============================================================================
  // Analysis Statistics
  // ============================================================================

  /**
   * Get comprehensive analysis statistics
   * Requirements: 需求 4.6, 需求 6.1, 需求 6.2
   */
  static async getAnalysisStatistics(options: StatisticsOptions = {}): Promise<AnalysisStatistics> {
    const mysql = await import('mysql2/promise');
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'trading_analysis',
      charset: 'utf8mb4',
      timezone: '+00:00',
    };

    const connection = await mysql.createConnection(dbConfig);

    try {
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (options.date_from) {
        whereClause += ' AND created_at >= ?';
        params.push(options.date_from);
      }

      if (options.date_to) {
        whereClause += ' AND created_at <= ?';
        params.push(options.date_to);
      }

      if (options.ticker) {
        whereClause += ' AND ticker = ?';
        params.push(options.ticker);
      }

      // Get basic statistics
      const [basicStats] = await connection.execute(
        `SELECT 
          COUNT(*) as total_workflows,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_workflows,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_workflows,
          AVG(CASE WHEN status = 'completed' AND started_at IS NOT NULL AND completed_at IS NOT NULL 
              THEN TIMESTAMPDIFF(SECOND, started_at, completed_at) END) as avg_duration_seconds
         FROM workflows ${whereClause}`,
        params
      );

      // Get success rates by ticker
      const [successRates] = await connection.execute(
        `SELECT 
          ticker,
          COUNT(*) as total_count,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as success_count
         FROM workflows ${whereClause}
         GROUP BY ticker
         ORDER BY total_count DESC
         LIMIT 10`,
        params
      );

      const basicStatsArray = basicStats as any[];
      const successRatesArray = successRates as any[];

      return {
        basic: basicStatsArray[0],
        successRates: successRatesArray,
      };
    } finally {
      await connection.end();
    }
  }

  /**
   * Get analysis reports with filtering and pagination
   * Requirements: 需求 4.1, 需求 4.2, 需求 4.3
   */
  static async getAnalysisReports(
    workflowId: string,
    type?: 'analyst' | 'research' | 'all'
  ): Promise<{ analystReports: AnalystReport[]; researchReports: ResearchReport[] }> {
    const mysql = await import('mysql2/promise');
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'trading_analysis',
      charset: 'utf8mb4',
      timezone: '+00:00',
    };

    const connection = await mysql.createConnection(dbConfig);

    try {
      let analystReports: AnalystReport[] = [];
      let researchReports: ResearchReport[] = [];

      if (type === 'analyst' || type === 'all' || !type) {
        const [analystResults] = await connection.execute(
          `SELECT ar.*, 
                  tad.trading_signal, tad.trend_signal, tad.support_level, tad.resistance_level,
                  tad.stop_loss_level, tad.target_price, tad.rsi_value, tad.macd_signal, tad.key_levels,
                  sad.overall_sentiment, sad.sentiment_score, sad.positive_news_count, 
                  sad.negative_news_count, sad.neutral_news_count, sad.key_drivers
           FROM analyst_reports ar
           LEFT JOIN technical_analysis_details tad ON ar.report_id = tad.report_id
           LEFT JOIN sentiment_analysis_details sad ON ar.report_id = sad.report_id
           WHERE ar.workflow_id = ? AND ar.status = 'completed'
           ORDER BY ar.created_at`,
          [workflowId]
        );
        analystReports = analystResults as AnalystReport[];
      }

      if (type === 'research' || type === 'all' || !type) {
        const [researchResults] = await connection.execute(
          `SELECT rr.*, 
                  GROUP_CONCAT(
                    CONCAT(ra.argument_type, ':', ra.content, '|', COALESCE(ra.strength_score, 0))
                    ORDER BY ra.sequence_order SEPARATOR ';;'
                  ) as arguments_data
           FROM research_reports rr
           LEFT JOIN research_arguments ra ON rr.report_id = ra.report_id
           WHERE rr.workflow_id = ? AND rr.status = 'completed'
           GROUP BY rr.report_id
           ORDER BY rr.created_at`,
          [workflowId]
        );
        researchReports = researchResults as ResearchReport[];
      }

      return { analystReports, researchReports };
    } finally {
      await connection.end();
    }
  }

  // ============================================================================
  // Utility Methods
  // ============================================================================

  /**
   * Check if workflow exists and is accessible
   * Requirements: 需求 7.1, 需求 7.2
   */
  static async validateWorkflowAccess(workflowId: string, userId?: string): Promise<boolean> {
    const mysql = await import('mysql2/promise');
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'trading_analysis',
      charset: 'utf8mb4',
      timezone: '+00:00',
    };

    const connection = await mysql.createConnection(dbConfig);

    try {
      const [results] = await connection.execute(
        'SELECT COUNT(*) as count FROM workflows WHERE workflow_id = ?',
        [workflowId]
      );

      return (results as any[])[0].count > 0;
    } finally {
      await connection.end();
    }
  }

  // ============================================================================
  // Private Helper Methods
  // ============================================================================

  private static generateUniqueId(prefix: string): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }
}

export default EnhancedLangGraphDatabase;
