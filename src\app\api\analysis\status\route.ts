import LangGraphDatabase from '@/lib/langgraph-database';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workflowId = searchParams.get('workflow_id');
    const taskId = searchParams.get('task_id');

    if (!workflowId && !taskId) {
      return NextResponse.json(
        { success: false, message: '需要提供 workflow_id 或 task_id' },
        { status: 400 }
      );
    }

    let actualWorkflowId = workflowId;

    // 如果只提供了 task_id，构造 workflow_id
    if (!actualWorkflowId && taskId) {
      actualWorkflowId = taskId.startsWith('wf_') ? taskId : `wf_${taskId}`;
    }

    // 直接调用数据库操作获取工作流信息
    const workflow = await LangGraphDatabase.getWorkflow(actualWorkflowId!);

    if (!workflow) {
      return NextResponse.json({ success: false, message: '工作流不存在' }, { status: 404 });
    }

    // 获取相关数据
    const { EnhancedLangGraphDatabase } = await import('@/lib/enhanced-langgraph-database');
    const completeStatus = await EnhancedLangGraphDatabase.getCompleteWorkflowStatus(
      actualWorkflowId!
    );
    const analystReports = completeStatus.analystReports;
    const researchReports = completeStatus.researchReports;
    const finalDecision = completeStatus.finalDecision;
    // 注意：riskAssessment 可能需要单独处理，暂时设为null
    const riskAssessment = null;

    // 构造工作流详情
    // 从 workflow_id 中提取 task_id（去掉 "wf_" 前缀）
    const extractedTaskId = workflow.workflow_id.startsWith('wf_')
      ? workflow.workflow_id.substring(3)
      : workflow.workflow_id;

    const workflowDetails = {
      workflow,
      status: {
        workflow_id: workflow.workflow_id,
        task_id: extractedTaskId,
        ticker: workflow.ticker,
        workflow_status: workflow.status,
        progress: workflow.progress,
        current_stage: workflow.current_stage,
        workflow_created_at: workflow.created_at,
        workflow_completed_at: workflow.completed_at,

        // 各阶段完成状态（基于报告存在性判断）
        fundamental_completed: analystReports.some((r) => r.analyst_type === 'fundamental'),
        technical_completed: analystReports.some((r) => r.analyst_type === 'technical'),
        sentiment_completed: analystReports.some((r) => r.analyst_type === 'sentiment'),
        news_completed: analystReports.some((r) => r.analyst_type === 'news'),
        bull_completed: researchReports.some((r) => r.researcher_type === 'bull'),
        bear_completed: researchReports.some((r) => r.researcher_type === 'bear'),
        consensus_completed: researchReports.length >= 2, // 需要多头和空头都完成
        risk_assessment_completed: !!riskAssessment,
        decision_completed: !!finalDecision,

        // 统计信息
        total_workflow_messages: 0, // 可以从 workflow_events 表获取
        max_debate_round: 0, // 可以从 debate_sessions 表获取
        duration_seconds:
          workflow.completed_at && workflow.started_at
            ? Math.floor(
                (new Date(workflow.completed_at).getTime() -
                  new Date(workflow.started_at).getTime()) /
                  1000
              )
            : 0,
      },
      analysts: {
        fundamental: analystReports.find((r) => r.analyst_type === 'fundamental'),
        technical: analystReports.find((r) => r.analyst_type === 'technical'),
        sentiment: analystReports.find((r) => r.analyst_type === 'sentiment'),
        news: analystReports.find((r) => r.analyst_type === 'news'),
      },
      researchers: {
        bull: researchReports.find((r) => r.researcher_type === 'bull'),
        bear: researchReports.find((r) => r.researcher_type === 'bear'),
      },
      consensus: null, // 可以从 consensus_evaluations 表获取
      risk: riskAssessment,
      decision: finalDecision,
    };

    // 转换为前端需要的格式
    const analysisStatus = {
      workflowId: workflowDetails.workflow?.workflow_id,
      taskId: workflowDetails.status?.task_id,
      ticker: workflowDetails.workflow?.ticker,
      status: workflowDetails.status?.workflow_status || 'pending',
      progress: workflowDetails.status?.progress || 0,
      currentStage: workflowDetails.status?.current_stage || 'pending',

      // 各阶段完成状态
      stages: {
        fundamental: workflowDetails.status?.fundamental_completed || false,
        technical: workflowDetails.status?.technical_completed || false,
        sentiment: workflowDetails.status?.sentiment_completed || false,
        news: workflowDetails.status?.news_completed || false,
        bull: workflowDetails.status?.bull_completed || false,
        bear: workflowDetails.status?.bear_completed || false,
        consensus: workflowDetails.status?.consensus_completed || false,
        risk: workflowDetails.status?.risk_assessment_completed || false,
        decision: workflowDetails.status?.decision_completed || false,
      },

      // 统计信息
      statistics: {
        totalMessages: workflowDetails.status?.total_workflow_messages || 0,
        debateRounds: workflowDetails.status?.max_debate_round || 0,
        duration: workflowDetails.status?.duration_seconds || 0,
      },

      // 时间信息
      createdAt: workflowDetails.workflow?.created_at,
      startedAt: workflowDetails.status?.workflow_created_at,
      completedAt: workflowDetails.status?.workflow_completed_at,

      // 最新报告预览
      latestReports: {
        analysts: workflowDetails.analysts || {},
        researchers: workflowDetails.researchers || {},
        consensus: workflowDetails.consensus,
        risk: workflowDetails.risk,
        decision: workflowDetails.decision,
      },
    };

    return NextResponse.json({
      success: true,
      data: analysisStatus,
    });
  } catch (error) {
    console.error('获取分析状态失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '获取分析状态失败',
      },
      { status: 500 }
    );
  }
}
