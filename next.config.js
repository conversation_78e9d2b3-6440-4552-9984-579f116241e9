// 在服务器端立即设置全局变量以防止 'self is not defined' 错误
if (typeof global !== 'undefined' && typeof global.self === 'undefined') {
  const mockGlobal = {
    addEventListener: () => { },
    removeEventListener: () => { },
    dispatchEvent: () => false,
    toString: () => '[object MockGlobal]',
    valueOf: () => null,
  };

  global.self = mockGlobal;
  global.window = mockGlobal;
  global.document = mockGlobal;
  global.navigator = mockGlobal;
  global.location = mockGlobal;
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用standalone输出模式用于Docker部署
  output: 'standalone',

  // 启用压缩
  compress: true,

  env: {
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000',
    NEXT_PUBLIC_WS_URL: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000',
    BACK_END_URL: process.env.BACK_END_URL || 'http://127.0.0.1:5000',
  },

  // 图片优化配置
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 604800, // 1 week
    dangerouslyAllowSVG: false,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // 实验性功能
  experimental: {
    optimizeCss: true,
    optimizePackageImports: [
      'lucide-react',
      '@heroicons/react',
      'framer-motion',
      'recharts',
      'react-hot-toast',
    ],
  },

  // 响应头配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=900, s-maxage=900', // 15 minutes
          },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400, immutable', // 1 day
          },
        ],
      },
      {
        source: '/_next/image(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=604800', // 1 week
          },
        ],
      },
      {
        source: '/fonts/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=2592000, immutable', // 1 month
          },
        ],
      },
    ];
  },
  webpack: (config, { dev, isServer }) => {
    // 处理 Node.js 内置模块
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        async_hooks: false,
        fs: false,
        path: false,
        os: false,
        crypto: false,
        stream: false,
        util: false,
        buffer: false,
        events: false,
        string_decoder: false,
      };
    } else {
      // 服务器端全局变量处理
      config.resolve.fallback = {
        ...config.resolve.fallback,
        'self': false,
        'window': false,
        'document': false,
        'navigator': false,
        'location': false,
      };
    }

    // 处理全局变量
    const webpack = require('webpack');

    // 定义全局变量类型检查
    config.plugins.push(
      new webpack.DefinePlugin({
        'typeof window': isServer ? JSON.stringify('undefined') : JSON.stringify('object'),
        'typeof document': isServer ? JSON.stringify('undefined') : JSON.stringify('object'),
        'typeof self': isServer ? JSON.stringify('undefined') : JSON.stringify('object'),
        'typeof navigator': isServer ? JSON.stringify('undefined') : JSON.stringify('object'),
        'typeof location': isServer ? JSON.stringify('undefined') : JSON.stringify('object'),
      })
    );

    // 为服务器端定义全局变量为 undefined
    if (isServer) {
      config.plugins.push(
        new webpack.DefinePlugin({
          'self': 'undefined',
          'window': 'undefined',
          'document': 'undefined',
          'navigator': 'undefined',
          'location': 'undefined',
        })
      );
    }

    // 忽略可能有问题的库在服务器端的导入
    config.externals = config.externals || [];
    if (!isServer) {
      config.externals.push({
        '@langchain/langgraph': 'commonjs @langchain/langgraph',
      });
    } else {
      // 服务器端忽略可能使用浏览器API的库
      config.externals.push({
        'socket.io-client': 'commonjs socket.io-client',
        'framer-motion': 'commonjs framer-motion',
        'gsap': 'commonjs gsap',
        'web-vitals': 'commonjs web-vitals',
      });

      // 忽略可能有问题的 SEO 文件
      config.resolve.alias = {
        ...config.resolve.alias,
        '@/lib/seo/performance-monitor': false,
        '@/lib/seo/mobile-seo-optimizer': false,
        '@/lib/seo/mobile-seo-tester': false,
      };
    }

    // 生产环境优化
    if (!dev) {
      // 启用 Tree Shaking
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;

      // 代码分割优化
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
            reuseExistingChunk: true,
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 5,
            reuseExistingChunk: true,
          },
          styles: {
            name: 'styles',
            test: /\.css$/,
            chunks: 'all',
            enforce: true,
          },
        },
      };
    }

    // Bundle 分析（当 ANALYZE=true 时）
    if (process.env.ANALYZE === 'true') {
      const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
          reportFilename: isServer ? '../analyze/server.html' : './analyze/client.html',
        })
      );
    }

    return config;
  },
};

module.exports = nextConfig;
