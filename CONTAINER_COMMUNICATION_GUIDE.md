# TradingAgents 容器间通信配置指南

## 问题描述

在 CI/CD 部署过程中，前端容器（3000 端口）和后端容器（5000 端口）无法互相通信，因为它们被部署在不同的 Docker Compose 文件中，没有共享网络。

## 解决方案概述

通过以下方式实现容器间通信：

1. **统一网络**: 创建共享的 Docker 网络 `tradingagents-network`
2. **容器命名**: 使用固定的容器名称进行内部通信
3. **环境变量配置**: 正确配置 API 地址使用容器名而非 localhost
4. **CORS 配置**: 后端允许前端容器的跨域请求

## 文件结构

```
project/
├── docker/
│   ├── Dockerfile                          # 前端Dockerfile
│   ├── docker-compose.prod.yml             # 完整部署配置
│   ├── docker-compose.prod.frontend.yml    # 前端独立部署
│   └── docker-compose.prod.backend.yml     # 后端独立部署
├── backend/akshare-service/
│   └── Dockerfile                          # 后端Dockerfile
├── .github/workflows/
│   └── ci.yml                              # CI/CD配置
└── scripts/
    └── deploy-with-network.sh              # 部署脚本
```

## 关键配置

### 1. Docker 网络配置

所有 Docker Compose 文件都使用统一的网络：

```yaml
networks:
  tradingagents-network:
    driver: bridge
    name: tradingagents-network
    external: true # 前端配置使用外部网络
    # external: false # 后端配置创建网络
```

### 2. 容器命名

- 前端容器: `tradingagents-frontend`
- 后端容器: `tradingagents-akshare-backend`
- MySQL 容器: `tradingagents-mysql`

### 3. 环境变量配置

#### 前端环境变量 (docker-compose.prod.frontend.yml)

```yaml
environment:
  # 使用容器名进行内部通信
  - NEXT_PUBLIC_API_BASE_URL=http://tradingagents-akshare-backend:5000
  - NEXT_PUBLIC_API_BACKEND_BASE_URL=http://tradingagents-akshare-backend:5000
  - BACK_END_URL=http://tradingagents-akshare-backend:5000
  - AKSHARE_API_URL=http://tradingagents-akshare-backend:5000
```

#### 后端环境变量 (docker-compose.prod.backend.yml)

```yaml
environment:
  # CORS配置允许前端容器访问
  - ALLOWED_ORIGINS=["http://localhost:3000","http://tradingagents-frontend:3000","${FRONTEND_URL:-http://localhost:3000}"]
```

## 部署方式

### 方式 1: 使用 CI/CD 自动部署

CI/CD 支持三种部署模式：

```bash
# 部署所有服务
workflow_dispatch: deploy_services = "all"

# 仅部署前端
workflow_dispatch: deploy_services = "frontend"

# 仅部署后端
workflow_dispatch: deploy_services = "backend"
```

### 方式 2: 使用部署脚本

```bash
# 给脚本执行权限
chmod +x scripts/deploy-with-network.sh

# 部署所有服务
./scripts/deploy-with-network.sh all

# 仅部署后端
./scripts/deploy-with-network.sh backend

# 仅部署前端
./scripts/deploy-with-network.sh frontend

# 查看状态
./scripts/deploy-with-network.sh status
```

### 方式 3: 手动 Docker Compose 部署

```bash
# 1. 创建网络（如果不存在）
docker network create tradingagents-network

# 2. 部署后端服务
docker-compose -f docker/docker-compose.prod.backend.yml --env-file .env.production up -d

# 3. 部署前端服务
docker-compose -f docker/docker-compose.prod.frontend.yml --env-file .env.production up -d

# 4. 验证网络连接
docker exec tradingagents-frontend ping -c 2 tradingagents-akshare-backend
```

## 网络通信验证

### 1. 检查容器状态

```bash
docker ps --filter "name=tradingagents-"
```

### 2. 检查网络连接

```bash
# 查看网络信息
docker network inspect tradingagents-network

# 测试前端到后端的连接
docker exec tradingagents-frontend ping tradingagents-akshare-backend

# 测试HTTP连接
docker exec tradingagents-frontend wget --spider http://tradingagents-akshare-backend:5000/health
```

### 3. 检查服务可访问性

```bash
# 前端服务
curl http://localhost:3000

# 后端API
curl http://localhost:5000/health

# 后端API文档
curl http://localhost:5000/docs
```

## 环境配置文件

创建 `.env.production` 文件：

```env
NODE_ENV=production
NEXT_PUBLIC_API_BASE_URL=http://localhost:5000
NEXT_PUBLIC_API_BACKEND_BASE_URL=http://localhost:5000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key
NEXT_PUBLIC_FINNHUB_API_KEY=your_finnhub_api_key
BACK_END_URL=http://localhost:5000
AKSHARE_API_URL=http://localhost:5000
MYSQL_ROOT_PASSWORD=trading123
MYSQL_DATABASE=trading_analysis
MYSQL_USER=trading_user
MYSQL_PASSWORD=trading123
```

## 故障排除

### 1. 容器无法互相通信

**症状**: 前端无法访问后端 API

**解决方案**:

```bash
# 检查网络配置
docker network ls | grep tradingagents

# 检查容器是否在同一网络
docker network inspect tradingagents-network

# 重新创建网络
docker network rm tradingagents-network
docker network create tradingagents-network
```

### 2. 前端环境变量配置错误

**症状**: 前端显示 API 连接错误

**解决方案**:

```bash
# 检查前端容器环境变量
docker exec tradingagents-frontend env | grep API

# 重新部署前端
docker-compose -f docker/docker-compose.prod.frontend.yml --env-file .env.production up -d --force-recreate
```

### 3. 后端 CORS 错误

**症状**: 浏览器控制台显示 CORS 错误

**解决方案**:

```bash
# 检查后端CORS配置
docker exec tradingagents-akshare-backend env | grep ALLOWED_ORIGINS

# 更新后端配置并重启
docker-compose -f docker/docker-compose.prod.backend.yml --env-file .env.production up -d --force-recreate
```

## 最佳实践

1. **网络隔离**: 使用专用网络避免与其他服务冲突
2. **容器命名**: 使用有意义的容器名称便于管理
3. **健康检查**: 配置健康检查确保服务正常运行
4. **日志管理**: 配置日志轮转避免磁盘空间问题
5. **环境分离**: 使用不同的环境配置文件
6. **监控**: 定期检查容器状态和网络连接

## 相关命令速查

```bash
# 网络管理
docker network create tradingagents-network
docker network ls
docker network inspect tradingagents-network
docker network rm tradingagents-network

# 容器管理
docker ps --filter "name=tradingagents-"
docker logs tradingagents-frontend
docker logs tradingagents-akshare-backend
docker exec -it tradingagents-frontend sh
docker exec -it tradingagents-akshare-backend bash

# 服务管理
docker-compose -f docker/docker-compose.prod.yml up -d
docker-compose -f docker/docker-compose.prod.yml down
docker-compose -f docker/docker-compose.prod.yml logs -f

# 清理
docker container prune
docker image prune
docker network prune
```

## 总结

通过以上配置，前端容器（3000 端口）和后端容器（5000 端口）可以通过 Docker 网络进行通信：

- **内部通信**: 容器间使用容器名进行通信（如 `http://tradingagents-akshare-backend:5000`）
- **外部访问**: 用户通过宿主机端口访问服务（如 `http://localhost:3000`）
- **网络隔离**: 所有服务在专用网络中运行，提高安全性
- **灵活部署**: 支持独立部署前端或后端服务

这样的配置确保了容器间的可靠通信，同时保持了部署的灵活性。
