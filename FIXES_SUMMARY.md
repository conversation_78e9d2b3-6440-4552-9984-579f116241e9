# 容器通信配置修复总结

## 修复的问题

### 1. API 地址配置错误

**问题**: 前端配置中使用容器名作为浏览器端 API 地址，导致浏览器无法访问后端。

**修复**:

- `NEXT_PUBLIC_*` 环境变量（浏览器端）使用 `http://localhost:5000`
- `BACK_END_URL` 和 `AKSHARE_API_URL`（服务端）使用容器名 `http://tradingagents-akshare-backend:5000`

### 2. Docker 网络配置冲突

**问题**: 前端和后端配置文件中网络设置不一致，导致网络创建冲突。

**修复**:

- 移除 `external: true/false` 配置
- 统一使用简单的网络配置
- 让 Docker Compose 自动处理网络创建

### 3. CI/CD 网络创建逻辑

**问题**: 网络创建失败会导致部署中断。

**修复**:

- 简化网络创建命令
- 添加错误容忍，即使网络已存在也继续部署

## 修复后的配置

### 前端独立部署 (docker-compose.prod.frontend.yml)

```yaml
environment:
  # 浏览器端API地址
  - NEXT_PUBLIC_API_BASE_URL=http://localhost:5000
  # 服务端API地址（容器间通信）
  - BACK_END_URL=http://tradingagents-akshare-backend:5000
```

### 后端独立部署 (docker-compose.prod.backend.yml)

```yaml
environment:
  # CORS允许前端访问
  - ALLOWED_ORIGINS=["http://localhost:3000","http://tradingagents-frontend:3000"]
```

### 完整部署 (docker-compose.prod.yml)

```yaml
environment:
  # 前端配置同上
  - NEXT_PUBLIC_API_BASE_URL=http://localhost:5000
  - BACK_END_URL=http://tradingagents-akshare-backend:5000
```

## 关键概念

### 浏览器端 vs 服务端 API 调用

- **浏览器端** (`NEXT_PUBLIC_*`): 用户浏览器发起的请求，必须使用外部地址
- **服务端** (`BACK_END_URL`): Next.js 服务端发起的请求，可以使用容器名

### 网络通信流程

```
用户浏览器 → http://localhost:3000 → 前端容器
前端容器 → http://tradingagents-akshare-backend:5000 → 后端容器
用户浏览器 → http://localhost:5000 → 后端容器（直接API调用）
```

## 部署验证

### 1. 检查容器状态

```bash
docker ps --filter "name=tradingagents-"
```

### 2. 检查网络连接

```bash
docker network inspect tradingagents-network
```

### 3. 测试 API 连接

```bash
# 外部访问
curl http://localhost:5000/health

# 容器间通信
docker exec tradingagents-frontend wget -qO- http://tradingagents-akshare-backend:5000/health
```

## 使用方法

### 自动部署（推荐）

通过 GitHub Actions 触发部署，系统会自动处理网络配置。

### 手动部署

```bash
# 使用部署脚本
./scripts/deploy-with-network.sh all

# 或手动执行
docker network create tradingagents-network 2>/dev/null || true
docker-compose -f docker/docker-compose.prod.yml up -d
```

## 总结

修复后的配置确保了：

1. 浏览器可以正常访问前端和后端服务
2. 前端服务端可以通过容器名访问后端
3. 网络配置简单可靠，避免冲突
4. 部署过程容错性更强

所有服务现在可以正常通信，前端(3000 端口)和后端(5000 端口)都能正常工作。
