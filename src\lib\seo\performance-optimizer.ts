/**
 * 性能优化工具集
 * 提供各种性能优化策略和工具
 */

export class PerformanceOptimizer {
  /**
   * 优化 LCP (Largest Contentful Paint)
   */
  static optimizeLCP(): void {
    if (typeof window === 'undefined') return;
    if (typeof window === 'undefined') return;

    // 预加载关键资源
    this.preloadCriticalResources();

    // 优化图片加载
    this.optimizeImageLoading();

    // 移除阻塞渲染的资源
    this.removeRenderBlockingResources();
  }

  /**
   * 优化 FID (First Input Delay)
   */
  static optimizeFID(): void {
    if (typeof window === 'undefined') return;
    if (typeof window === 'undefined') return;

    // 分解长任务
    this.breakUpLongTasks();

    // 延迟加载非关键 JavaScript
    this.deferNonCriticalJS();

    // 使用 Web Workers 处理计算密集型任务
    this.setupWebWorkers();
  }

  /**
   * 优化 CLS (Cumulative Layout Shift)
   */
  static optimizeCLS(): void {
    if (typeof window === 'undefined') return;
    if (typeof window === 'undefined') return;

    // 为图片和视频设置尺寸属性
    this.setImageDimensions();

    // 预留广告和嵌入内容的空间
    this.reserveSpaceForAds();

    // 避免在现有内容上方插入内容
    this.avoidContentInsertion();
  }

  /**
   * 预加载关键资源
   */
  private static preloadCriticalResources(): void {
    if (typeof document === 'undefined') return;
    const criticalResources = [
      { href: '/tradingAgent.png', as: 'image' },
      { href: '/fonts/inter-var.woff2', as: 'font', type: 'font/woff2', crossorigin: 'anonymous' },
    ];

    criticalResources.forEach((resource) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource.href;
      link.as = resource.as;
      if (resource.type) link.type = resource.type;
      if (resource.crossorigin) link.crossOrigin = resource.crossorigin;

      // 避免重复添加
      if (!document.querySelector(`link[href="${resource.href}"]`)) {
        document.head.appendChild(link);
      }
    });
  }

  /**
   * 优化图片加载
   */
  private static optimizeImageLoading(): void {
    if (typeof document === 'undefined') return;
    // 为所有图片添加 loading="lazy" 属性（除了首屏图片）
    const images = document.querySelectorAll('img:not([loading])');
    images.forEach((img, index) => {
      // 首屏的前3张图片不使用懒加载
      if (index >= 3) {
        img.setAttribute('loading', 'lazy');
      }
    });

    // 为图片添加 decoding="async" 属性
    document.querySelectorAll('img:not([decoding])').forEach((img) => {
      img.setAttribute('decoding', 'async');
    });
  }

  /**
   * 移除阻塞渲染的资源
   */
  private static removeRenderBlockingResources(): void {
    if (typeof document === 'undefined') return;
    // 为非关键 CSS 添加 media 属性
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]:not([media])');
    stylesheets.forEach((link) => {
      const href = link.getAttribute('href');
      if (href && !href.includes('critical')) {
        link.setAttribute('media', 'print');
        link.addEventListener('load', () => {
          link.setAttribute('media', 'all');
        });
      }
    });
  }

  /**
   * 分解长任务
   */
  private static breakUpLongTasks(): void {
    if (typeof window === 'undefined') return;
    // 使用 scheduler.postTask 或 setTimeout 分解长任务
    const originalSetTimeout = window.setTimeout;

    (window as any).setTimeout = function (callback: Function, delay: number = 0, ...args: any[]) {
      // 如果延迟时间很短，使用 scheduler.postTask（如果可用）
      if (delay < 5 && 'scheduler' in window && 'postTask' in (window as any).scheduler) {
        return (window as any).scheduler.postTask(callback, { delay });
      }

      return originalSetTimeout.apply(window, [callback as TimerHandler, delay, ...args] as any);
    };
  }

  /**
   * 延迟加载非关键 JavaScript
   */
  private static deferNonCriticalJS(): void {
    if (typeof document === 'undefined') return;
    // 标记非关键脚本并延迟加载
    const scripts = document.querySelectorAll('script[data-defer="true"]');
    scripts.forEach((script) => {
      const newScript = document.createElement('script');
      newScript.src = script.getAttribute('src') || '';
      newScript.defer = true;

      // 在页面加载完成后加载
      if (document.readyState === 'complete') {
        document.head.appendChild(newScript);
      } else {
        window.addEventListener('load', () => {
          document.head.appendChild(newScript);
        });
      }
    });
  }

  /**
   * 设置 Web Workers
   */
  private static setupWebWorkers(): void {
    if (typeof window === 'undefined') return;
    // 为计算密集型任务创建 Web Worker
    if ('Worker' in window) {
      // 这里可以设置具体的 Web Worker 逻辑
      console.log('Web Workers available for performance optimization');
    }
  }

  /**
   * 为图片设置尺寸属性
   */
  private static setImageDimensions(): void {
    if (typeof document === 'undefined') return;
    const images = document.querySelectorAll('img:not([width]):not([height])');
    images.forEach((img) => {
      const imgElement = img as HTMLImageElement;
      // 如果图片已经加载，获取其自然尺寸
      if (imgElement.complete && imgElement.naturalWidth > 0) {
        imgElement.setAttribute('width', imgElement.naturalWidth.toString());
        imgElement.setAttribute('height', imgElement.naturalHeight.toString());
      } else {
        // 为未加载的图片设置默认尺寸
        imgElement.addEventListener('load', () => {
          if (!imgElement.getAttribute('width') && !imgElement.getAttribute('height')) {
            imgElement.setAttribute('width', imgElement.naturalWidth.toString());
            imgElement.setAttribute('height', imgElement.naturalHeight.toString());
          }
        });
      }
    });
  }

  /**
   * 为广告和嵌入内容预留空间
   */
  private static reserveSpaceForAds(): void {
    if (typeof document === 'undefined') return;
    // 为动态内容容器设置最小高度
    const dynamicContainers = document.querySelectorAll('[data-dynamic-content]');
    dynamicContainers.forEach((container) => {
      const minHeight = container.getAttribute('data-min-height');
      if (minHeight && !(container as HTMLElement).style.minHeight) {
        (container as HTMLElement).style.minHeight = minHeight;
      }
    });
  }

  /**
   * 避免在现有内容上方插入内容
   */
  private static avoidContentInsertion(): void {
    if (typeof window === 'undefined') return;
    // 监控 DOM 变化，警告可能导致 CLS 的操作
    if ('MutationObserver' in window) {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // 检查是否在视口内添加了内容
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as Element;
                const rect = element.getBoundingClientRect();

                // 如果在视口内添加内容，发出警告
                if (rect.top < window.innerHeight && rect.left < window.innerWidth) {
                  console.warn('Content inserted in viewport, may cause CLS:', element);
                }
              }
            });
          }
        });
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });
    }
  }

  /**
   * 初始化所有性能优化
   */
  static initializeOptimizations(): void {
    if (typeof window === 'undefined') return;

    // 等待 DOM 加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.applyOptimizations();
      });
    } else {
      this.applyOptimizations();
    }
  }

  /**
   * 应用所有优化策略
   */
  private static applyOptimizations(): void {
    this.optimizeLCP();
    this.optimizeFID();
    this.optimizeCLS();

    console.log('Performance optimizations applied');
  }

  /**
   * 获取性能建议
   */
  static getPerformanceRecommendations(): string[] {
    if (typeof document === 'undefined') return [];
    const recommendations: string[] = [];

    // 检查图片优化
    const unoptimizedImages = document.querySelectorAll('img:not([loading]):not([decoding])');
    if (unoptimizedImages.length > 0) {
      recommendations.push(`优化 ${unoptimizedImages.length} 张图片的加载属性`);
    }

    // 检查字体加载
    const fonts = document.querySelectorAll('link[rel="preload"][as="font"]');
    if (fonts.length === 0) {
      recommendations.push('预加载关键字体以改善 LCP');
    }

    // 检查 CSS 阻塞
    const blockingCSS = document.querySelectorAll('link[rel="stylesheet"]:not([media])');
    if (blockingCSS.length > 2) {
      recommendations.push('考虑内联关键 CSS 或使用媒体查询延迟加载');
    }

    return recommendations;
  }
}
